using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Configuration;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;

namespace Purolator.SmartSort.Windows.Services.AddressValidationService
{
    delegate void SendMethod(Entities.PrePrintDetails item);

    public partial class AddressValidationService : ServiceBase
    {
        
        private static Timer timer;

        private static int ProcessPeriodInSeconds = AddressValidationSettings.Default.ProcessPeriodInSeconds;
        private static int ConcurrentWorkers = AddressValidationSettings.Default.ConcurrentWorkers;
        private static int FetchAmount = AddressValidationSettings.Default.FetchAmount;
        private static string ConnectionInfo = string.Empty;     

        public static string DB_CONNECTION_PROPERTY = "SSDB";


        public AddressValidationService()
        {
            InitializeComponent();
        }

        

        protected override void OnStart(string[] args)
        {
            AppSettingsReader settingsReader = new AppSettingsReader();
            ConnectionInfo = (string)settingsReader.GetValue(DB_CONNECTION_PROPERTY, typeof(String));      
            AddressValidationServiceImplementation.ResetDatabaseQueue(ConnectionInfo);
            TimerCallback tmrCallBack = new TimerCallback(Timer_TimerCallback);
            timer = new Timer(tmrCallBack);            
            timer.Change(new TimeSpan(0, 0, 1), new TimeSpan(0, 0, ProcessPeriodInSeconds));
        }

        protected override void OnStop()
        {
        }


        private void Timer_TimerCallback(object state)
        {
            Logger.Debug("Timer wakes up!", LogCategories.ADDRESS_VALIDATION);
            //Manually stop the timer...           
            timer.Change(Timeout.Infinite, Timeout.Infinite);     
            
            // reload the config
            AddressValidationSettings.Default.Reload();
            ProcessPeriodInSeconds = AddressValidationSettings.Default.ProcessPeriodInSeconds;
            ConcurrentWorkers = AddressValidationSettings.Default.ConcurrentWorkers;
            FetchAmount = AddressValidationSettings.Default.FetchAmount;
            AppSettingsReader settingsReader = new AppSettingsReader();
            ConnectionInfo = (string)settingsReader.GetValue(DB_CONNECTION_PROPERTY, typeof(String));                    
            
            DoWork();
         
            // set back the timer
            timer.Change(new TimeSpan(0, 0, ProcessPeriodInSeconds), new TimeSpan(0, 0, ProcessPeriodInSeconds));                      
        }
       

        private void DoWork()
        {
            try
            {
                List<Entities.PrePrintDetails> items = AddressValidationServiceImplementation.GetWorkItems(FetchAmount);

                SendMethod workerMethod = AddressValidationServiceImplementation.Process;

                var workerBlock = new ActionBlock<Entities.PrePrintDetails>(                    
                    actionEvent => workerMethod(actionEvent),
                    // Specify a maximum degree of parallelism. 
                    new ExecutionDataflowBlockOptions
                    {
                        MaxDegreeOfParallelism = ConcurrentWorkers
                    });

                foreach (var item in items)
                {
                    workerBlock.Post(item);
                }

                workerBlock.Complete();
                workerBlock.Completion.Wait();
            }
           catch (Exception ex)
            {
                Logger.Error("Error in Enterprise Event worker", LogCategories.ADDRESS_VALIDATION, ex);            
            }
        }
    }
}
