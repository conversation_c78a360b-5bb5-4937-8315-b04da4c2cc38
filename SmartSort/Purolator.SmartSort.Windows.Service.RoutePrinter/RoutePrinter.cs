﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Configuration;
using System.Text;
using System.Threading;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;


namespace Purolator.SmartSort.Windows.Service.RoutePrinter
{
    public partial class RoutePrinter : ServiceBase
    {

        private static Timer timer;

        private int ProcessPeriodInSeconds = 1300;
        private int FetchAmount = 2;        
                        

        public RoutePrinter()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            AppSettingsReader settings = new AppSettingsReader();
            ProcessPeriodInSeconds = (int) settings.GetValue("ProcessPeriodInSeconds", typeof(int));
            FetchAmount = (int)settings.GetValue("FetchAmount", typeof(int));
            RoutePrinterImplementation implementation = new RoutePrinterImplementation();
            implementation.ResetDatabaseQueue();
            TimerCallback tmrCallBack = new TimerCallback(Timer_TimerCallback);
            timer = new Timer(tmrCallBack);
            timer.Change(new TimeSpan(0, 0, 1), new TimeSpan(0, 0, ProcessPeriodInSeconds));
        }

        protected override void OnStop()
        {
        }

        private void Timer_TimerCallback(object state)
        {
            Logger.Debug("Timer wakes up!", LogCategories.DEFAULT);
            //Manually stop the timer...           
            timer.Change(Timeout.Infinite, Timeout.Infinite);

            AppSettingsReader settings = new AppSettingsReader();
            ProcessPeriodInSeconds = (int)settings.GetValue("ProcessPeriodInSeconds", typeof(int));
            FetchAmount = (int)settings.GetValue("FetchAmount", typeof(int));

            DoWork();

            // set back the timer
            Logger.Debug("Timer goes to sleep again!", LogCategories.PRINT_SERVICE);
            timer.Change(new TimeSpan(0, 0, ProcessPeriodInSeconds), new TimeSpan(0, 0, ProcessPeriodInSeconds));
        }


        private void DoWork()
        {
            try
            {
                RoutePrinterImplementation implementation = new RoutePrinterImplementation(); 
                var items = implementation.GetWorkItems(FetchAmount);
                Logger.Debug("QUEUE got " + items.Count + "records", LogCategories.PRINT_SERVICE);

                while (items.Count > 0)
                {
                    foreach (var item in items)
                    {
                        implementation.Process(item);
                    }

                    items = implementation.GetWorkItems(FetchAmount);
                    Logger.Debug("QUEUE got " + items.Count + "records", LogCategories.PRINT_SERVICE);
                }                 
            }
            catch (Exception ex)
            {
                Logger.Error("Error in Route printer worker", LogCategories.PRINT_SERVICE, ex);
            }
        }
    }
}
